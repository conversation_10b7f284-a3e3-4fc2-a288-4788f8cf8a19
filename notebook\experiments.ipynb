{"cells": [{"cell_type": "code", "execution_count": 31, "id": "6a65c63f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["all_ok\n"]}], "source": ["print(\"all_ok\")"]}, {"cell_type": "code", "execution_count": 32, "id": "de80b62c", "metadata": {}, "outputs": [], "source": ["from langchain_groq import ChatGroq"]}, {"cell_type": "code", "execution_count": 33, "id": "6aa2bdcc", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv"]}, {"cell_type": "code", "execution_count": 34, "id": "4515cdf3", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv()"]}, {"cell_type": "code", "execution_count": 35, "id": "05e3bc43", "metadata": {}, "outputs": [], "source": ["llm=ChatGroq(model=\"deepseek-r1-distill-llama-70b\")"]}, {"cell_type": "code", "execution_count": 36, "id": "9ffaaf4b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "\n", "</think>\n", "\n", "The capital of France is Paris.\n"]}], "source": ["print(llm.invoke(\"What is the capital of France?\").content)"]}, {"cell_type": "code", "execution_count": 37, "id": "df5c2e5f", "metadata": {}, "outputs": [], "source": ["from langchain_google_genai import GoogleGenerativeAIEmbeddings"]}, {"cell_type": "code", "execution_count": 38, "id": "e6adec1f", "metadata": {}, "outputs": [], "source": ["embedding_model=GoogleGenerativeAIEmbeddings(model=\"models/embedding-001\")"]}, {"cell_type": "code", "execution_count": 72, "id": "bfcacd34", "metadata": {}, "outputs": [], "source": ["doc_vector=embedding_model.embed_query(\"What is the capital of France?\")"]}, {"cell_type": "markdown", "id": "792ae86b", "metadata": {}, "source": ["## 1. Data Ingestion"]}, {"cell_type": "code", "execution_count": 1, "id": "ff8dcbc8", "metadata": {}, "outputs": [], "source": ["from langchain.document_loaders import PyPDFLoader"]}, {"cell_type": "code", "execution_count": 2, "id": "8feb528d", "metadata": {}, "outputs": [], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter"]}, {"cell_type": "code", "execution_count": 3, "id": "68999868", "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": 5, "id": "f32486a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["os.getcwd()"]}, {"cell_type": "code", "execution_count": 6, "id": "0253c52a", "metadata": {}, "outputs": [], "source": ["file_path=os.path.join(os.getcwd(), \"data\", \"sample.pdf\")"]}, {"cell_type": "code", "execution_count": 8, "id": "1e9d58b2", "metadata": {}, "outputs": [], "source": ["loader=PyPDFLoader(file_path)"]}, {"cell_type": "code", "execution_count": 9, "id": "fd2ae1dc", "metadata": {}, "outputs": [], "source": ["documents=loader.load()"]}, {"cell_type": "code", "execution_count": null, "id": "376cb8a4", "metadata": {}, "outputs": [], "source": ["documents"]}, {"cell_type": "code", "execution_count": 11, "id": "6670c7ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["77"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(documents)"]}, {"cell_type": "markdown", "id": "c1bc6e89", "metadata": {}, "source": ["### this is a experimental thing there is no deterministic way to split the text"]}, {"cell_type": "code", "execution_count": 58, "id": "2e5f06d6", "metadata": {}, "outputs": [], "source": ["text_splitter=RecursiveCharacterTextSplitter(\n", "    chunk_size=500,\n", "    chunk_overlap=150,\n", "    length_function=len\n", ")"]}, {"cell_type": "code", "execution_count": 59, "id": "361efa05", "metadata": {}, "outputs": [], "source": ["docs=text_splitter.split_documents(documents)"]}, {"cell_type": "code", "execution_count": 60, "id": "53ed226c", "metadata": {}, "outputs": [{"data": {"text/plain": ["765"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["len(docs)"]}, {"cell_type": "code", "execution_count": 16, "id": "5f15a31e", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 0, 'page_label': '1'}, page_content='Llama 2: Open Foundation and Fine-Tuned Chat Models\\nHugo Touvron∗ <PERSON>† <PERSON>†\\nPeter Albert <PERSON> Soumya Batra\\nPrajjwal Bhargava Shruti Bhosale Dan Bikel Lukas Blecher Cristian <PERSON>\\nGuillem Cucurull <PERSON>\\nCynthia Gao Vedanuj Goswami <PERSON> Goyal Anthony <PERSON>shorn <PERSON>ar <PERSON>ini Rui Hou\\n<PERSON>akan Inan <PERSON>s <PERSON>habsa <PERSON> Kloumann <PERSON>m <PERSON>renev')"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[0]"]}, {"cell_type": "code", "execution_count": 17, "id": "72cddd95", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'producer': 'pdfTeX-1.40.25',\n", " 'creator': 'LaTeX with hyperref',\n", " 'creationdate': '2023-07-20T00:30:36+00:00',\n", " 'author': '',\n", " 'keywords': '',\n", " 'moddate': '2023-07-20T00:30:36+00:00',\n", " 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5',\n", " 'subject': '',\n", " 'title': '',\n", " 'trapped': '/False',\n", " 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf',\n", " 'total_pages': 77,\n", " 'page': 0,\n", " 'page_label': '1'}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[0].metadata"]}, {"cell_type": "code", "execution_count": 26, "id": "ce96aa5b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'producer': 'pdfTeX-1.40.25',\n", " 'creator': 'LaTeX with hyperref',\n", " 'creationdate': '2023-07-20T00:30:36+00:00',\n", " 'author': '',\n", " 'keywords': '',\n", " 'moddate': '2023-07-20T00:30:36+00:00',\n", " 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5',\n", " 'subject': '',\n", " 'title': '',\n", " 'trapped': '/False',\n", " 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf',\n", " 'total_pages': 77,\n", " 'page': 0,\n", " 'page_label': '1'}"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[1].metadata"]}, {"cell_type": "code", "execution_count": null, "id": "96181a04", "metadata": {}, "outputs": [], "source": ["0,1,2,3\n", "page_lable=4\n", "actual page is 4 \n", "\n", "page_index=3\n", "page_lable=4"]}, {"cell_type": "code", "execution_count": 27, "id": "865ed5e2", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'producer': 'pdfTeX-1.40.25',\n", " 'creator': 'LaTeX with hyperref',\n", " 'creationdate': '2023-07-20T00:30:36+00:00',\n", " 'author': '',\n", " 'keywords': '',\n", " 'moddate': '2023-07-20T00:30:36+00:00',\n", " 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5',\n", " 'subject': '',\n", " 'title': '',\n", " 'trapped': '/False',\n", " 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf',\n", " 'total_pages': 77,\n", " 'page': 3,\n", " 'page_label': '4'}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[100].metadata"]}, {"cell_type": "code", "execution_count": 28, "id": "3fd5afde", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'producer': 'pdfTeX-1.40.25',\n", " 'creator': 'LaTeX with hyperref',\n", " 'creationdate': '2023-07-20T00:30:36+00:00',\n", " 'author': '',\n", " 'keywords': '',\n", " 'moddate': '2023-07-20T00:30:36+00:00',\n", " 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5',\n", " 'subject': '',\n", " 'title': '',\n", " 'trapped': '/False',\n", " 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf',\n", " 'total_pages': 77,\n", " 'page': 7,\n", " 'page_label': '8'}"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[200].metadata"]}, {"cell_type": "code", "execution_count": 18, "id": "ec192b03", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Llama 2: Open Foundation and Fine-Tuned Chat Models\\nHugo Touvron∗ <PERSON>† <PERSON>†\\nPeter Albert <PERSON><PERSON> Bashlykov Soumya Batra\\nPrajjwal Bhargava Shruti Bhosale Dan Bikel Lukas Blecher Cristian Canton Ferrer Moya Chen\\nGuillem Cucurull David <PERSON>\\nCynthia Gao Vedanuj Goswami Naman Goyal Anthony <PERSON> Saghar Hosseini Rui Hou\\nHakan Inan <PERSON><PERSON> Korene<PERSON>'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[0].page_content"]}, {"cell_type": "code", "execution_count": 29, "id": "f92af725", "metadata": {}, "outputs": [], "source": ["from langchain.vectorstores import FAISS"]}, {"cell_type": "code", "execution_count": 40, "id": "9ded599c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Llama 2: Open Foundation and Fine-Tuned Chat Models\\nHugo Touvron∗ <PERSON>† <PERSON>†\\nPeter <PERSON>um<PERSON> Batra'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[0].page_content"]}, {"cell_type": "code", "execution_count": 71, "id": "6835d49f", "metadata": {}, "outputs": [], "source": ["doc_vector=embedding_model.embed_documents(docs[0].page_content)"]}, {"cell_type": "code", "execution_count": 42, "id": "2c109387", "metadata": {}, "outputs": [{"data": {"text/plain": ["768"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["len(embedding_model.embed_documents(docs[0].page_content)[0])"]}, {"cell_type": "code", "execution_count": 43, "id": "ed952e03", "metadata": {}, "outputs": [], "source": ["vectorstore=FAISS.from_documents(docs, embedding_model)"]}, {"cell_type": "code", "execution_count": null, "id": "1487e254", "metadata": {}, "outputs": [], "source": ["token->words\n", "\n", "chunk--> it is collection of words(token)[characters]"]}, {"cell_type": "markdown", "id": "3e769025", "metadata": {}, "source": ["1. in memory(faiss is in memory vector store,chroma)\n", "2. on disk storage(faiss you can persist over the disk,chroma)\n", "3. cloud storage(cloud variant of faiss is not available)(pinecone,weaviate,milvus,mongodbvectorsearch,astradb)"]}, {"cell_type": "markdown", "id": "decd5d79", "metadata": {}, "source": ["## This is a Retrieval proceesss\n", "\n", "means from the vectordatabase we are going to fetch or retrive or rank the most appropriate k result"]}, {"cell_type": "code", "execution_count": 46, "id": "55503be2", "metadata": {}, "outputs": [], "source": ["relevant_doc=vectorstore.similarity_search(\"llama2 finetuning benchmark experiments.\")"]}, {"cell_type": "code", "execution_count": 52, "id": "da52627f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='6289bcec-31dc-48ff-84fa-d69a9655495a', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 2, 'page_label': '3'}, page_content='this paper contributes a thorough description of our fine-tuning methodology and approach to improving'),\n", " Document(id='75b176ce-4658-4ef0-bc85-b05445f93f3a', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 6, 'page_label': '7'}, page_content='any publicly reported results.\\nIn Table 3, we summarize the overall performance across a suite of popular benchmarks. Note that safety'),\n", " Document(id='b2cb4a7c-7366-4888-946e-f8605b6af2e7', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 6, 'page_label': '7'}, page_content='benchmarks are shared in Section 4.1. The benchmarks are grouped into the categories listed below. The\\nresults for all the individual benchmarks are available in Section A.2.2.'),\n", " Document(id='b8c2d93b-c55c-403b-b51e-4696affef8df', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 21, 'page_label': '22'}, page_content='partially made up of programming code data.\\nSafetyBenchmarksforPretrainedModels. Weevaluatethesafetycapabilitiesof Llama 2onthreepopular')]"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["relevant_doc"]}, {"cell_type": "code", "execution_count": 48, "id": "b59e667e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'this paper contributes a thorough description of our fine-tuning methodology and approach to improving'"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["relevant_doc[0].page_content"]}, {"cell_type": "code", "execution_count": 49, "id": "8d2f345b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'any publicly reported results.\\nIn Table 3, we summarize the overall performance across a suite of popular benchmarks. Note that safety'"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["relevant_doc[1].page_content"]}, {"cell_type": "code", "execution_count": 50, "id": "4c52e60d", "metadata": {}, "outputs": [{"data": {"text/plain": ["'benchmarks are shared in Section 4.1. The benchmarks are grouped into the categories listed below. The\\nresults for all the individual benchmarks are available in Section A.2.2.'"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["relevant_doc[2].page_content"]}, {"cell_type": "code", "execution_count": 51, "id": "01f6ff55", "metadata": {}, "outputs": [{"data": {"text/plain": ["'partially made up of programming code data.\\nSafetyBenchmarksforPretrainedModels. Weevaluatethesafetycapabilitiesof Llama 2onthreepopular'"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["relevant_doc[3].page_content"]}, {"cell_type": "code", "execution_count": 53, "id": "6a971b78", "metadata": {}, "outputs": [], "source": ["relevant_doc=vectorstore.similarity_search(\"llama2 finetuning benchmark experiments.\",k=10)"]}, {"cell_type": "code", "execution_count": 54, "id": "b687780f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='6289bcec-31dc-48ff-84fa-d69a9655495a', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 2, 'page_label': '3'}, page_content='this paper contributes a thorough description of our fine-tuning methodology and approach to improving'),\n", " Document(id='75b176ce-4658-4ef0-bc85-b05445f93f3a', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 6, 'page_label': '7'}, page_content='any publicly reported results.\\nIn Table 3, we summarize the overall performance across a suite of popular benchmarks. Note that safety'),\n", " Document(id='b2cb4a7c-7366-4888-946e-f8605b6af2e7', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 6, 'page_label': '7'}, page_content='benchmarks are shared in Section 4.1. The benchmarks are grouped into the categories listed below. The\\nresults for all the individual benchmarks are available in Section A.2.2.'),\n", " Document(id='b8c2d93b-c55c-403b-b51e-4696affef8df', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 21, 'page_label': '22'}, page_content='partially made up of programming code data.\\nSafetyBenchmarksforPretrainedModels. Weevaluatethesafetycapabilitiesof Llama 2onthreepopular'),\n", " Document(id='e097e958-0e22-4803-b47b-38152106b6a3', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 21, 'page_label': '22'}, page_content='descriptions of the benchmarks and metrics can be found in Appendix A.4.7. When compared toLlama 1-7B,'),\n", " Document(id='26d11b87-ba5c-4d85-94d2-d1f760888cf1', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 35, 'page_label': '36'}, page_content='7 Conclusion\\nIn this study, we have introducedLlama 2, a new family of pretrained and fine-tuned models with scales'),\n", " Document(id='65017910-5871-45e1-9160-df168297aaef', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 1, 'page_label': '2'}, page_content='2.3 Llama 2Pretrained Model Evaluation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 7\\n3 Fine-tuning 8'),\n", " Document(id='f7ab7e0c-a6c1-4798-890a-68f44d762a37', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 18, 'page_label': '19'}, page_content='tasks. There are relatively few public benchmarks for these contexts, so we feel sharing our analysis here will\\nbenefit the research community.'),\n", " Document(id='aee4b7f8-6afe-4c48-9ffc-c44ef6b589a1', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 47, 'page_label': '48'}, page_content='2 models and others open-source models.\\nStandard Benchmarks. In Table 20, we show results on several standard benchmarks.'),\n", " Document(id='0d36328d-dc33-4c31-8495-9aed39b01929', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 12, 'page_label': '13'}, page_content='models obtain higher performance for a similar volume of data. More importantly, the scaling performance')]"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["relevant_doc"]}, {"cell_type": "code", "execution_count": 56, "id": "67ff01f9", "metadata": {}, "outputs": [], "source": ["relevant_doc=vectorstore.similarity_search(\"llama2 finetuning benchmark experiments.\",k=1)"]}, {"cell_type": "code", "execution_count": 57, "id": "c2c9f69f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='6289bcec-31dc-48ff-84fa-d69a9655495a', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 2, 'page_label': '3'}, page_content='this paper contributes a thorough description of our fine-tuning methodology and approach to improving')]"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["relevant_doc"]}, {"cell_type": "code", "execution_count": null, "id": "0c39292e", "metadata": {}, "outputs": [], "source": ["# retriever=vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "ecc8481a", "metadata": {}, "source": ["### you can explore about keyword filtering"]}, {"cell_type": "code", "execution_count": 82, "id": "2df7b858", "metadata": {}, "outputs": [], "source": ["retriever = vectorstore.as_retriever(search_kwargs={\"k\": 10})"]}, {"cell_type": "code", "execution_count": 83, "id": "4d5f07ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='6289bcec-31dc-48ff-84fa-d69a9655495a', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 2, 'page_label': '3'}, page_content='this paper contributes a thorough description of our fine-tuning methodology and approach to improving'),\n", " Document(id='75b176ce-4658-4ef0-bc85-b05445f93f3a', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 6, 'page_label': '7'}, page_content='any publicly reported results.\\nIn Table 3, we summarize the overall performance across a suite of popular benchmarks. Note that safety'),\n", " Document(id='b2cb4a7c-7366-4888-946e-f8605b6af2e7', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 6, 'page_label': '7'}, page_content='benchmarks are shared in Section 4.1. The benchmarks are grouped into the categories listed below. The\\nresults for all the individual benchmarks are available in Section A.2.2.'),\n", " Document(id='b8c2d93b-c55c-403b-b51e-4696affef8df', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 21, 'page_label': '22'}, page_content='partially made up of programming code data.\\nSafetyBenchmarksforPretrainedModels. Weevaluatethesafetycapabilitiesof Llama 2onthreepopular'),\n", " Document(id='e097e958-0e22-4803-b47b-38152106b6a3', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 21, 'page_label': '22'}, page_content='descriptions of the benchmarks and metrics can be found in Appendix A.4.7. When compared toLlama 1-7B,'),\n", " Document(id='26d11b87-ba5c-4d85-94d2-d1f760888cf1', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 35, 'page_label': '36'}, page_content='7 Conclusion\\nIn this study, we have introducedLlama 2, a new family of pretrained and fine-tuned models with scales'),\n", " Document(id='65017910-5871-45e1-9160-df168297aaef', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 1, 'page_label': '2'}, page_content='2.3 Llama 2Pretrained Model Evaluation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 7\\n3 Fine-tuning 8'),\n", " Document(id='f7ab7e0c-a6c1-4798-890a-68f44d762a37', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 18, 'page_label': '19'}, page_content='tasks. There are relatively few public benchmarks for these contexts, so we feel sharing our analysis here will\\nbenefit the research community.'),\n", " Document(id='aee4b7f8-6afe-4c48-9ffc-c44ef6b589a1', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 47, 'page_label': '48'}, page_content='2 models and others open-source models.\\nStandard Benchmarks. In Table 20, we show results on several standard benchmarks.'),\n", " Document(id='0d36328d-dc33-4c31-8495-9aed39b01929', metadata={'producer': 'pdfTeX-1.40.25', 'creator': 'LaTeX with hyperref', 'creationdate': '2023-07-20T00:30:36+00:00', 'author': '', 'keywords': '', 'moddate': '2023-07-20T00:30:36+00:00', 'ptex.fullbanner': 'This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) kpathsea version 6.3.5', 'subject': '', 'title': '', 'trapped': '/False', 'source': 'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook\\\\data\\\\sample.pdf', 'total_pages': 77, 'page': 12, 'page_label': '13'}, page_content='models obtain higher performance for a similar volume of data. More importantly, the scaling performance')]"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["retriever.invoke(\"llama2 finetuning benchmark experiments.\")"]}, {"cell_type": "markdown", "id": "0a5a2e3a", "metadata": {}, "source": ["## Question: user question\n", "## Context: based on the question retrieving the info from the vector database"]}, {"cell_type": "code", "execution_count": 84, "id": "32650c54", "metadata": {}, "outputs": [], "source": ["prompt_template = \"\"\"\n", "        Answer the question based on the context provided below. \n", "        If the context does not contain sufficient information, respond with: \n", "        \"I do not have enough information about this.\"\n", "\n", "        Context: {context}\n", "\n", "        Question: {question}\n", "\n", "        Answer:\"\"\"\n"]}, {"cell_type": "code", "execution_count": 85, "id": "9189656c", "metadata": {}, "outputs": [], "source": ["from langchain.prompts import PromptTemplate"]}, {"cell_type": "code", "execution_count": 86, "id": "ccc6ccf2", "metadata": {}, "outputs": [], "source": ["prompt=PromptTemplate(\n", "    template=prompt_template,\n", "    input_variables=[\"context\", \"question\"]\n", ")"]}, {"cell_type": "code", "execution_count": 87, "id": "82058939", "metadata": {}, "outputs": [{"data": {"text/plain": ["PromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, template='\\n        Answer the question based on the context provided below. \\n        If the context does not contain sufficient information, respond with: \\n        \"I do not have enough information about this.\"\\n\\n        Context: {context}\\n\\n        Question: {question}\\n\\n        Answer:')"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt"]}, {"cell_type": "markdown", "id": "f45dc8fe", "metadata": {}, "source": ["PromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, template='\\n        Answer the question based on the context provided below. \\n        If the context does not contain sufficient information, respond with: \\n        \"I do not have enough information about this.\"\\n\\n        Context: {context}\\n\\n        Question: {question}\\n\\n        Answer:')"]}, {"cell_type": "code", "execution_count": 88, "id": "d5b39444", "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser"]}, {"cell_type": "code", "execution_count": 89, "id": "f17d8d90", "metadata": {}, "outputs": [], "source": ["parser=StrOutputParser()"]}, {"cell_type": "code", "execution_count": 90, "id": "3ef826e7", "metadata": {}, "outputs": [], "source": ["def format_docs(docs):\n", "    return \"\\n\\n\".join([doc.page_content for doc in docs])"]}, {"cell_type": "code", "execution_count": 91, "id": "fbcf20aa", "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnablePassthrough"]}, {"cell_type": "code", "execution_count": 92, "id": "b10cb466", "metadata": {}, "outputs": [], "source": ["rag_chain = (\n", "    {\"context\": retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"]}, {"cell_type": "code", "execution_count": 93, "id": "0c3976d0", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"<think>\\nOkay, so I need to answer the question about the Llama 2 fine-tuning benchmark experiments based on the provided context. Let me read through the context carefully to extract relevant information.\\n\\nFirst, the context mentions Table 3, which summarizes the overall performance across various benchmarks. It refers to Appendix A.4.7 for safety descriptions and metrics. However, the main details about the experiments are in Section 3, which covers fine-tuning. \\n\\nIn Section 3, the context talks about using supervised fine-tuning, including both instruction tuning and RLHF (Reinforcement Learning with Human Feedback). It mentions that these methods required significant computational and annotation resources. There's a reference to InstructionTuning by Wei<PERSON>. (2021), which achieved zero-shot performance on unseen tasks by fine-tuning LLMs.\\n\\nThe context also points out that Section A.2.2 has results for individual benchmarks, but since that's not provided here, I can't access those specific details. \\n\\nSo, putting it together, the answer should mention that Llama 2's fine-tuning involved supervised methods like instruction tuning and RLHF, required substantial resources, and refers to <PERSON><PERSON><PERSON>'s work. Since detailed benchmark results are in a section not provided, I should note that individual results are available elsewhere but not here.\\n</think>\\n\\nThe Llama 2 fine-tuning benchmark experiments involved supervised fine-tuning methods, including instruction tuning and RLHF (Reinforcement Learning with Human Feedback). These approaches required significant computational and annotation resources. The experiments built on work by <PERSON>etal. (2021), which achieved zero-shot performance on unseen tasks through fine-tuning. While detailed benchmark results are available in Section A.2.2, this specific information is not provided in the given context.\""]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["rag_chain.invoke(\"tell  me about the llama2 finetuning benchmark experiments?\")"]}, {"cell_type": "code", "execution_count": 96, "id": "ca813139", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"<think>\\nOkay, so I need to figure out the scaling trends for the reward model based on the provided context. Let me read through the context carefully.\\n\\nFirst, the context mentions that Figure 6 shows scaling trends for the reward model. It says that more data and a larger model size generally improve accuracy. It also notes that the models haven't yet saturated from the training data, which means there's still room for improvement.\\n\\nLooking further, there's a mention of tuning different model sizes on increasing amounts of reward model data collected each week. The answers are based on a 7-point Likert scale, where higher is better. The reward models overall perform better on more distinct responses than similar pairs.\\n\\nThere's also a section on scaling trends in terms of data and model size for the reward model, both fine-tuned and on-distribution. It talks about maintaining accurate rewards for the latest model. Table 6 presents statistics of reward modeling data collected over time, showing how the models predict a scalar for a single output without requiring retraining each time. \\n\\nAdditionally, it's noted that reward model accuracy is one of the most important factors and that there's still room for improvement with more annotations. The reward models can predict accurately, but there's potential for more enhancement as the data and model size increase.\\n\\nSo, putting this all together, the scaling trends indicate that as more data is added and the model size increases, the reward model's accuracy improves. The models haven't reached their limit yet, so further scaling should continue to enhance performance.\\n</think>\\n\\nThe scaling trends for the reward model, as shown in Figure 6, indicate that both an increase in data and a larger model size lead to improved accuracy. The models have not yet reached their learning saturation point, suggesting that there is still potential for further improvement with additional data and model scaling. The reward models perform better on more distinct responses compared to similar pairs, and they maintain accurate rewards for the latest models. Overall, the trends highlight the positive impact of scaling on reward model performance.\""]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["rag_chain.invoke(\"can you tell me Scaling trends for the reward model?\")"]}, {"cell_type": "markdown", "id": "4826f5b4", "metadata": {}, "source": ["# One Small task"]}, {"cell_type": "markdown", "id": "26672a6a", "metadata": {}, "source": ["### Take 10 pdfs keep it in same directory and create RAG on top of it."]}, {"cell_type": "markdown", "id": "cde1345b", "metadata": {}, "source": ["In next class will discuss about the(will start with the modular coding)\n", "1. exception module\n", "2. logger module\n", "3. doc analyser\n", "4. doc compare\n", "5. utils and config\n", "   \n", "2 class\n", "\n", "2 more class api and other module\n", "\n", "2 more class for deployment"]}, {"cell_type": "markdown", "id": "ede1a8f2", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}